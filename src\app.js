const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Import configurations and routes
const { testConnection } = require('./config/db');
const routes = require('./api/routes');

/**
 * Archive Service - Express Application
 * 
 * This service acts as the "librarian" of the system, responsible for:
 * - Storing and retrieving persona assessment results
 * - Providing secure access to user's assessment history
 * - Accepting updates from Analysis Worker service
 * - Maintaining data integrity for archive_schema.persona_profiles
 */

const app = express();
const PORT = process.env.PORT || 3003;

// ============================================================================
// MIDDLEWARE CONFIGURATION
// ============================================================================

/**
 * CORS Configuration
 * Allow requests from API Gateway and other internal services
 */
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

/**
 * Body parsing middleware
 * Parse JSON requests with size limit for security
 */
app.use(express.json({ 
  limit: '10mb',  // Allow larger payloads for persona_result data
  strict: true 
}));

/**
 * URL-encoded data parsing
 */
app.use(express.urlencoded({ 
  extended: true, 
  limit: '10mb' 
}));

/**
 * Request logging middleware (development)
 */
if (process.env.NODE_ENV === 'development') {
  app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
  });
}

/**
 * Security headers
 */
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  next();
});

// ============================================================================
// ROUTES
// ============================================================================

/**
 * Health check endpoint (public)
 * Used by load balancers and monitoring systems
 */
app.get('/health', (req, res) => {
  res.status(200).json({
    service: 'archive-service',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

/**
 * Root endpoint
 */
app.get('/', (req, res) => {
  res.status(200).json({
    service: 'archive-service',
    description: 'Archive Service for persona assessment results',
    version: process.env.npm_package_version || '1.0.0',
    endpoints: {
      public: [
        'GET /profiles - Get user profiles (requires JWT)',
        'GET /profiles/:id - Get profile details (requires JWT)'
      ],
      internal: [
        'PATCH /internal/profiles/:id - Update profile status',
        'GET /internal/health - Internal health check'
      ]
    }
  });
});

/**
 * Main API routes
 * All profile-related endpoints are handled here
 */
app.use('/', routes);

// ============================================================================
// ERROR HANDLING
// ============================================================================

/**
 * 404 handler for undefined routes
 */
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.originalUrl} not found`,
    service: 'archive-service'
  });
});

/**
 * Global error handler
 */
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);
  
  // Handle specific error types
  if (error.type === 'entity.parse.failed') {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Invalid JSON in request body'
    });
  }
  
  if (error.type === 'entity.too.large') {
    return res.status(413).json({
      error: 'Payload Too Large',
      message: 'Request body exceeds size limit'
    });
  }
  
  // Default error response
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'An unexpected error occurred',
    service: 'archive-service'
  });
});

// ============================================================================
// SERVER STARTUP
// ============================================================================

/**
 * Start the server
 */
const startServer = async () => {
  try {
    // Test database connection before starting server
    await testConnection();
    
    // Start the Express server
    app.listen(PORT, () => {
      console.log(`🚀 Archive Service running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 Database: ${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`);
      console.log(`⏰ Started at: ${new Date().toISOString()}`);
    });
    
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start the server
startServer();

module.exports = app;
