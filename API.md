# Archive Service API Documentation

## Base URL
- Development: `http://localhost:3003`
- Production: `https://api.atma.com/archive` (via API Gateway)

## Authentication
Public endpoints require JWT authentication via Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Response Format
All responses follow this structure:
```json
{
  "success": boolean,
  "data": object | array,
  "message": string,
  "error": string,
  "count": number (for arrays)
}
```

## Error Codes
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (missing/invalid JWT)
- `404` - Not Found (profile not found or access denied)
- `500` - Internal Server Error

---

## Public Endpoints

### GET /profiles
Get all profiles for authenticated user.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "status": "completed",
      "created_at": "2024-01-15T10:30:00Z",
      "completed_at": "2024-01-15T10:35:00Z",
      "error_message": null
    }
  ],
  "count": 1
}
```

### GET /profiles/:profileId
Get specific profile details for authenticated user.

**Parameters:**
- `profileId` (UUID) - Profile identifier

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "status": "completed",
    "persona_result": {
      "personality_type": "INTJ",
      "traits": {
        "openness": 0.8,
        "conscientiousness": 0.9,
        "extraversion": 0.3,
        "agreeableness": 0.6,
        "neuroticism": 0.2
      },
      "description": "The Architect",
      "strengths": ["Strategic thinking", "Independence"],
      "weaknesses": ["Overly critical", "Perfectionist"]
    },
    "created_at": "2024-01-15T10:30:00Z",
    "completed_at": "2024-01-15T10:35:00Z",
    "error_message": null
  }
}
```

---

## Internal Endpoints

### PATCH /internal/profiles/:profileId
Update profile status and results (Analysis Worker only).

**Parameters:**
- `profileId` (UUID) - Profile identifier

**Request Body:**
```json
{
  "status": "completed",
  "persona_result": {
    "personality_type": "INTJ",
    "traits": {
      "openness": 0.8,
      "conscientiousness": 0.9
    }
  }
}
```

**For failed status:**
```json
{
  "status": "failed",
  "error_message": "Analysis failed due to insufficient data"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "status": "completed",
    "completed_at": "2024-01-15T10:35:00Z"
  }
}
```

### GET /internal/health
Internal health check for service monitoring.

**Response:**
```json
{
  "success": true,
  "service": "archive-service",
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

---

## Status Values

| Status | Description |
|--------|-------------|
| `processing` | Assessment is being analyzed |
| `completed` | Analysis completed successfully |
| `failed` | Analysis failed with error |

---

## Validation Rules

### Profile ID
- Must be valid UUID format
- Required in URL parameters

### Status Updates
- `status`: Required, must be one of: `processing`, `completed`, `failed`
- `persona_result`: Required when status is `completed`, must be valid JSON object
- `error_message`: Required when status is `failed`, max 1000 characters

---

## Example Usage

### Get User Profiles
```bash
curl -X GET http://localhost:3003/profiles \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### Get Profile Details
```bash
curl -X GET http://localhost:3003/profiles/550e8400-e29b-41d4-a716-************ \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### Update Profile (Internal)
```bash
curl -X PATCH http://localhost:3003/internal/profiles/550e8400-e29b-41d4-a716-************ \
  -H "Content-Type: application/json" \
  -d '{
    "status": "completed",
    "persona_result": {
      "personality_type": "INTJ",
      "traits": {"openness": 0.8}
    }
  }'
```

### Health Check
```bash
curl -X GET http://localhost:3003/health
curl -X GET http://localhost:3003/internal/health
```
