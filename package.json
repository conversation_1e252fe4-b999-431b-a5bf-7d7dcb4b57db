{"name": "archive-service", "version": "1.0.0", "description": "Archive Service for storing and retrieving persona assessment results", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "node --watch src/app.js", "test": "echo \"Error: no test specified\" && exit 1", "docker:build": "docker build -t archive-service .", "docker:run": "docker run -p 3003:3003 --env-file .env archive-service", "docker:compose:up": "docker-compose up -d", "docker:compose:down": "docker-compose down", "docker:compose:logs": "docker-compose logs -f archive-service", "lint": "echo \"No linter configured\"", "format": "echo \"No formatter configured\""}, "keywords": ["microservice", "archive", "express", "postgresql"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3"}}