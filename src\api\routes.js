const express = require('express');
const ProfileController = require('../controllers/profileController');
const { verifyToken } = require('../middleware/auth');
const { 
  validateProfileId, 
  validateGeneralProfileUpdate 
} = require('../middleware/validation');

const router = express.Router();

/**
 * API Routes for Archive Service
 * 
 * Public routes (accessed via API Gateway with /api/profiles prefix):
 * - GET /profiles - Get user's profile list
 * - GET /profiles/:profileId - Get specific profile details
 * 
 * Internal routes (accessed directly by other services):
 * - PATCH /internal/profiles/:profileId - Update profile status
 * - GET /internal/health - Health check
 */

// ============================================================================
// PUBLIC ROUTES (Require JWT Authentication)
// ============================================================================

/**
 * GET /profiles
 * Get all profiles for the authenticated user
 * Returns lightweight profile list for dashboard display
 * 
 * Headers: Authorization: Bearer <jwt_token>
 * Response: Array of profile metadata (id, status, created_at, completed_at)
 */
router.get('/profiles', verifyToken, ProfileController.getUserProfiles);

/**
 * GET /profiles/:profileId
 * Get specific profile details for the authenticated user
 * Returns complete profile data with security check
 * 
 * Headers: Authorization: Bearer <jwt_token>
 * Params: profileId (UUID)
 * Response: Complete profile data (excluding raw_input_data)
 */
router.get('/profiles/:profileId', 
  verifyToken, 
  validateProfileId, 
  ProfileController.getProfileDetails
);

// ============================================================================
// INTERNAL ROUTES (No JWT required - network-level security)
// ============================================================================

/**
 * PATCH /internal/profiles/:profileId
 * Update profile status and results (called by Analysis Worker)
 * 
 * Params: profileId (UUID)
 * Body: {
 *   status: 'processing' | 'completed' | 'failed',
 *   persona_result?: object,  // required if status is 'completed'
 *   error_message?: string    // required if status is 'failed'
 * }
 * Response: Updated profile metadata
 */
router.patch('/internal/profiles/:profileId',
  validateProfileId,
  validateGeneralProfileUpdate,
  ProfileController.updateProfileStatus
);

/**
 * GET /internal/health
 * Health check endpoint for internal services
 * 
 * Response: Service status and timestamp
 */
router.get('/internal/health', ProfileController.healthCheck);

// ============================================================================
// ERROR HANDLING
// ============================================================================

/**
 * 404 handler for undefined routes
 */
router.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.originalUrl} not found`,
    service: 'archive-service'
  });
});

/**
 * Global error handler for this router
 */
router.use((error, req, res, next) => {
  console.error('Router error:', error);
  
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'An unexpected error occurred',
    service: 'archive-service'
  });
});

module.exports = router;
