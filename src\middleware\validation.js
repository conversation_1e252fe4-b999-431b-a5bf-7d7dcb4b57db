const { body, param, validationResult } = require('express-validator');

/**
 * Validation Middleware
 * Contains validation rules for different endpoints
 */

/**
 * Validation rules for profile ID parameter
 * Ensures profileId is a valid UUID format
 */
const validateProfileId = [
  param('profileId')
    .isUUID()
    .withMessage('Profile ID must be a valid UUID')
    .notEmpty()
    .withMessage('Profile ID is required')
];

/**
 * Validation rules for updating profile status (internal endpoint)
 * Used by Analysis Worker to update profile results
 */
const validateProfileUpdate = [
  body('status')
    .isIn(['processing', 'completed', 'failed'])
    .withMessage('Status must be one of: processing, completed, failed')
    .notEmpty()
    .withMessage('Status is required'),
    
  body('persona_result')
    .optional()
    .isObject()
    .withMessage('Persona result must be a valid JSON object'),
    
  body('error_message')
    .optional()
    .isString()
    .withMessage('Error message must be a string')
    .isLength({ max: 1000 })
    .withMessage('Error message must not exceed 1000 characters')
];

/**
 * Validation rules for completed status updates
 * Ensures persona_result is provided when status is completed
 */
const validateCompletedUpdate = [
  ...validateProfileUpdate,
  body().custom((value) => {
    if (value.status === 'completed' && !value.persona_result) {
      throw new Error('Persona result is required when status is completed');
    }
    return true;
  })
];

/**
 * Validation rules for failed status updates
 * Ensures error_message is provided when status is failed
 */
const validateFailedUpdate = [
  ...validateProfileUpdate,
  body().custom((value) => {
    if (value.status === 'failed' && !value.error_message) {
      throw new Error('Error message is required when status is failed');
    }
    return true;
  })
];

/**
 * General validation for profile updates that handles all cases
 */
const validateGeneralProfileUpdate = [
  ...validateProfileUpdate,
  body().custom((value) => {
    // If status is completed, persona_result should be provided
    if (value.status === 'completed' && !value.persona_result) {
      throw new Error('Persona result is required when status is completed');
    }
    
    // If status is failed, error_message should be provided
    if (value.status === 'failed' && !value.error_message) {
      throw new Error('Error message is required when status is failed');
    }
    
    return true;
  })
];

/**
 * Middleware to handle validation errors
 * Should be used after validation rules to check for errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Invalid request data',
      details: errors.array()
    });
  }
  next();
};

module.exports = {
  validateProfileId,
  validateProfileUpdate,
  validateCompletedUpdate,
  validateFailedUpdate,
  validateGeneralProfileUpdate,
  handleValidationErrors
};
