-- Database initialization script for Archive Service
-- This script sets up the necessary schema, user, and table for the Archive Service

-- Create the archive_user with appropriate permissions
CREATE USER archive_user WITH PASSWORD 'archive_password';

-- Create the archive_schema
CREATE SCHEMA IF NOT EXISTS archive_schema;

-- Grant permissions to archive_user
GRANT USAGE ON SCHEMA archive_schema TO archive_user;
GRANT CREATE ON SCHEMA archive_schema TO archive_user;

-- Create the persona_profiles table
CREATE TABLE IF NOT EXISTS archive_schema.persona_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'processing' CHECK (status IN ('processing', 'completed', 'failed')),
    raw_input_data JSONB,
    persona_result JSONB,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_persona_profiles_user_id ON archive_schema.persona_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_persona_profiles_status ON archive_schema.persona_profiles(status);
CREATE INDEX IF NOT EXISTS idx_persona_profiles_created_at ON archive_schema.persona_profiles(created_at DESC);

-- Grant table permissions to archive_user
GRANT SELECT, INSERT, UPDATE, DELETE ON archive_schema.persona_profiles TO archive_user;

-- Create a trigger to automatically update the updated_at column
CREATE OR REPLACE FUNCTION archive_schema.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_persona_profiles_updated_at 
    BEFORE UPDATE ON archive_schema.persona_profiles 
    FOR EACH ROW 
    EXECUTE FUNCTION archive_schema.update_updated_at_column();

-- Insert some sample data for testing (optional)
INSERT INTO archive_schema.persona_profiles (
    user_id, 
    status, 
    raw_input_data, 
    persona_result,
    completed_at
) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440001',
    'completed',
    '{"responses": ["answer1", "answer2", "answer3"]}',
    '{"personality_type": "INTJ", "traits": {"openness": 0.8, "conscientiousness": 0.9}}',
    NOW() - INTERVAL '1 day'
),
(
    '550e8400-e29b-41d4-a716-446655440001',
    'processing',
    '{"responses": ["answer4", "answer5", "answer6"]}',
    NULL,
    NULL
),
(
    '550e8400-e29b-41d4-a716-446655440002',
    'failed',
    '{"responses": ["invalid_data"]}',
    NULL,
    NOW() - INTERVAL '2 hours'
) ON CONFLICT DO NOTHING;

-- Display success message
DO $$
BEGIN
    RAISE NOTICE 'Archive Service database initialization completed successfully!';
    RAISE NOTICE 'Schema: archive_schema';
    RAISE NOTICE 'User: archive_user';
    RAISE NOTICE 'Table: persona_profiles';
END $$;
