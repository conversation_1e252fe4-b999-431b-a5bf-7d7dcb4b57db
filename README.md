# Archive Service

Archive Service adalah microservice yang bertanggung jawab sebagai "pustakawan" sistem ATMA. Layanan ini menyimpan dan mengelola semua hasil asesmen persona yang telah dan sedang diproses.

## 🎯 Tujuan & Tanggung Jawab

- **Penyimpanan Data**: Menyediakan antarmuka untuk menyimpan dan memperbarui status serta hasil asesmen
- **Pengambilan Data Pengguna**: Endpoint aman untuk pengguna melihat riwayat dan hasil detail asesmen
- **Integritas Data**: Bertanggung jawab penuh atas tabel `archive_schema.persona_profiles`
- **Antarmuka Internal**: Endpoint khusus untuk layanan internal (Analysis Worker)

## 🏗️ Arsitektur

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │───▶│   API Gateway    │───▶│ Archive Service │
│   (React)       │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐                               ┌─────────────────┐
│ Analysis Worker │──────────────────────────────▶│   PostgreSQL    │
│   Service       │         Internal API          │   Database      │
└─────────────────┘                               └─────────────────┘
```

## 📡 API Endpoints

### Public Endpoints (melalui API Gateway)

#### `GET /profiles`
<PERSON><PERSON><PERSON> daftar riwayat asesmen milik pengguna yang sedang login.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid-profil-1",
      "status": "completed",
      "created_at": "2024-01-15T10:30:00Z",
      "completed_at": "2024-01-15T10:35:00Z"
    }
  ],
  "count": 1
}
```

#### `GET /profiles/:profileId`
Mengambil detail lengkap dari satu hasil asesmen.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid-profil-1",
    "status": "completed",
    "persona_result": {
      "personality_type": "INTJ",
      "traits": {
        "openness": 0.8,
        "conscientiousness": 0.9
      }
    },
    "created_at": "2024-01-15T10:30:00Z",
    "completed_at": "2024-01-15T10:35:00Z"
  }
}
```

### Internal Endpoints (akses langsung)

#### `PATCH /internal/profiles/:profileId`
Memperbarui status dan hasil analisis (dipanggil oleh Analysis Worker).

**Body:**
```json
{
  "status": "completed",
  "persona_result": {
    "personality_type": "INTJ",
    "traits": {
      "openness": 0.8,
      "conscientiousness": 0.9
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "id": "uuid-profil-1",
    "status": "completed",
    "completed_at": "2024-01-15T10:35:00Z"
  }
}
```

#### `GET /internal/health`
Health check untuk layanan internal.

## 🚀 Menjalankan Layanan

### Prasyarat
- Node.js 18+
- PostgreSQL 15+
- Docker (opsional)

### Environment Variables
Salin `.env.example` ke `.env` dan sesuaikan:

```bash
cp .env.example .env
```

### Development

```bash
# Install dependencies
npm install

# Jalankan dalam mode development
npm run dev

# Atau jalankan normal
npm start
```

### Menggunakan Docker

```bash
# Build dan jalankan dengan Docker Compose
npm run docker:compose:up

# Lihat logs
npm run docker:compose:logs

# Hentikan layanan
npm run docker:compose:down
```

### Manual Docker

```bash
# Build image
npm run docker:build

# Jalankan container
npm run docker:run
```

## 🗄️ Database Schema

```sql
CREATE TABLE archive_schema.persona_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'processing',
    raw_input_data JSONB,
    persona_result JSONB,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔒 Keamanan

- **JWT Authentication**: Semua endpoint publik memerlukan token JWT yang valid
- **Ownership Validation**: Query selalu menyertakan `user_id` untuk mencegah akses data pengguna lain
- **Network Isolation**: Endpoint internal hanya dapat diakses dalam jaringan Docker
- **Input Validation**: Validasi ketat untuk semua input menggunakan express-validator

## 🔧 Teknologi

- **Framework**: Express.js
- **Database**: PostgreSQL dengan node-postgres (pg)
- **Validation**: express-validator
- **Authentication**: jsonwebtoken
- **Environment**: dotenv
- **CORS**: cors

## 📁 Struktur Proyek

```
archive-service/
├── src/
│   ├── api/
│   │   └── routes.js          # Definisi endpoint
│   ├── config/
│   │   └── db.js              # Konfigurasi database
│   ├── controllers/
│   │   └── profileController.js # Logik bisnis
│   ├── middleware/
│   │   ├── auth.js            # JWT validation
│   │   └── validation.js      # Input validation
│   ├── models/
│   │   └── profileModel.js    # Database queries
│   └── app.js                 # Setup utama Express
├── .env.example
├── Dockerfile
├── docker-compose.yml
├── init-db.sql
└── package.json
```

## 🧪 Testing

```bash
# Jalankan tests (belum diimplementasi)
npm test

# Health check manual
curl http://localhost:3003/health
```

## 📊 Monitoring

- Health check endpoint: `GET /health`
- Internal health check: `GET /internal/health`
- Docker health check terintegrasi
- Logging untuk semua operasi database

## 🤝 Integrasi dengan Layanan Lain

- **Assessment Service**: Membuat entri awal dengan status 'processing'
- **Analysis Worker**: Memperbarui hasil analisis melalui endpoint internal
- **API Gateway**: Meneruskan request dari frontend dengan validasi JWT
- **Admin Service**: Mengakses data untuk dashboard admin

## 📝 Lisensi

ISC
